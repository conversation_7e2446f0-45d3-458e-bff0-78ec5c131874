package com.fpt.dxg.notification.util;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;

public class DateUtil {
    public static final String FORMAT_DATE_MONTH_YEAR = "dd/MM/yyyy";
    public static final String FORMAT_24H = "dd/MM/yyyy HH:mm:ss";
    public static final String FORMAT_VMG = "dd-MM-yyyy HH:mm";
    public static final String FORMAT_VHAT = "yyyy-mm-dd HH:MM:ss";

    public static String dateToString(Date date, String strFormat) {
        try {
            if (date != null) {
                SimpleDateFormat simpleDateFormat = new SimpleDateFormat(strFormat);
                return simpleDateFormat.format(date);
            } else
                return null;
        } catch (Exception e) {
            return null;
        }
    }

    public static Date stringToDate(String dateString, String strFormat) {
        try {
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat(strFormat);
            return simpleDateFormat.parse(dateString);
        } catch (ParseException e) {
            return null;
        }
    }
}
