package com.fpt.dxg.notification.util;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;

public class ValidationUtil {

//    static Validator validator = Validation.buildDefaultValidatorFactory().getValidator();

    public static boolean isNullOrEmpty(String st) {
        return st == null || st.isEmpty();
    }

    public static boolean isNullOrEmpty(Object obj) {
        return obj == null || obj.toString().isEmpty();
    }

    public static boolean notEmpty(List<?> lst) {
        return lst != null && !lst.isEmpty();
    }

    public static boolean isNullOrEmpty(Map<?, ?> map) {
        return map == null || map.isEmpty();
    }

    public static boolean isNullOrEmpty(Collection<?> collection) {
        return collection == null || collection.isEmpty();
    }

    public static boolean isUUIDString(String input) {
        Pattern pattern = Pattern.compile("^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$");
        return pattern.matcher(input).matches();
    }

    public static boolean isNull(Object object) {
        return object == null;
    }

//    public static void validation(Object input) throws ValidationException {
//        {
//            validator = Validation.buildDefaultValidatorFactory().getValidator();
//            Set<ConstraintViolation<Object>> violations = validator.validate(input);
//            List<String> details = new ArrayList<>();
//            for (ConstraintViolation<Object> error : violations) {
//                details.add(error.getMessageTemplate());
//            }
//            if (!details.isEmpty()) {
//                throw new ValidationException(String.join(", ", details));
//            }
//        }
//    }
}
