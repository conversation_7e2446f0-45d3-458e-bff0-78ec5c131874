package com.fpt.dxg.notification;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.scheduling.annotation.EnableScheduling;

import java.util.Optional;

@SpringBootApplication
@EnableScheduling
public class App implements CommandLineRunner {

    private static final Logger LOG = LogManager.getLogger(App.class);

    public static void main(String[] args) {
        SpringApplication.run(App.class, args);
    }

    @Override
    public void run(String... args) {
        Optional<String> version = Optional.ofNullable(App.class.getPackage().getImplementationVersion());
        LOG.info(String.format("Notification persistence %s starting...", version.orElse("")));
        LOG.info(String.format("Notification persistence %s started", version.orElse("")));
    }


}
