package com.fpt.dxg.notification.exception;

import com.fpt.dxg.notification.model.MessagesResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.context.request.WebRequest;

/**
 * Author: PhucVM
 * Date: 21/10/2019
 */
@SuppressWarnings({"rawtypes"})
@ControllerAdvice
@Slf4j
public class GlobalExceptionHandler {
    @ExceptionHandler({Exception.class})
    public final ResponseEntity<Object> handleAllException(Exception ex, WebRequest request) {
        MessagesResponse msg = new MessagesResponse();
        msg.error(ex);
        log.error(ex.getMessage(), ex);
        return new ResponseEntity<>(msg, HttpStatus.OK);
    }
}
