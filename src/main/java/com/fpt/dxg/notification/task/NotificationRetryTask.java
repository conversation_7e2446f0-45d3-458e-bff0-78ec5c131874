package com.fpt.dxg.notification.task;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fpt.dxg.notification.constant.Constants;
import com.fpt.dxg.notification.mail.MessageMail;
import com.fpt.dxg.notification.model.MailHistory;
import com.fpt.dxg.notification.model.MailRetry;
import com.fpt.dxg.notification.model.NotificationMessage;
import com.fpt.dxg.notification.service.MailService;
import com.fpt.dxg.notification.service.MailRetryService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Component
@Slf4j
public class NotificationRetryTask {

    private final MailRetryService mailRetryService;
    private final MailService mailService;
    private final ObjectMapper mapper;

    @Autowired
    public NotificationRetryTask(MailRetryService mailRetryService, MailService mailService, ObjectMapper mapper) {
        this.mailRetryService = mailRetryService;
        this.mailService = mailService;
        this.mapper = mapper;
    }

    //fix 1 tieng
    @Scheduled(fixedRate = 60 * 60 * 1000)
    public void retryEmail() {
        List<MailHistory> mailHistories = new ArrayList<>();
        List<MailRetry> mailNeedRetry = mailRetryService.getAllMailNeedRetry();
        MessageMail messageMail = null;
        String messageLog = null;
        boolean status = false;
        boolean isRetry = false;

        for(MailRetry noti : mailNeedRetry) {
            try {
                String payload = noti.getPayload();
                NotificationMessage message = mapper.readValue(payload, NotificationMessage.class);
                log.info("--- Retry message Email: {} {}", noti.getId(), noti.getPayload());

                messageMail = mapper.convertValue(message.getPayload(), MessageMail.class);
                mailService.sendMail(messageMail);

                noti.setStatus(Constants.STATUS_COMPLETED);
                messageLog = "Success";
                status = true;
                isRetry = true;
            } catch (Exception e) {
                messageLog = "Retry fail. Detail: " + e.getMessage();
                log.info("{}", messageLog);
            } finally {
                if(messageMail != null) {
                    mailHistories.add(new MailHistory(messageMail, messageLog, status, isRetry));
                }
            }
        }

        mailService.saveListMailHistory(mailHistories);
        mailRetryService.saveListMailRetry(mailNeedRetry);
    }


}

