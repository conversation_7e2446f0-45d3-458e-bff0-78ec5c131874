package com.fpt.dxg.notification.repository;

import com.fpt.dxg.notification.model.MailRetry;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface MailRetryRepository extends MongoRepository<MailRetry, String> {

    List<MailRetry> findAllByExpiredTimeAfterAndStatus(long expiredTime, long status);

}
