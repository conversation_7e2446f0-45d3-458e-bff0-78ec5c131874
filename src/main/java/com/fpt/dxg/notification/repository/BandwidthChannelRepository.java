package com.fpt.dxg.notification.repository;

import com.fpt.dxg.notification.model.BandwidthChannel;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface BandwidthChannelRepository extends MongoRepository<BandwidthChannel, String> {

    @Query("{key : ?0}")
    BandwidthChannel getByKey(String key);

}
