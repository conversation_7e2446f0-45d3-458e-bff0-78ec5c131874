package com.fpt.dxg.notification.config;

import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.core.task.TaskDecorator;

import java.util.Map;
@Slf4j
public class MDCTaskDecorator implements TaskDecorator {

    @Override
    public Runnable decorate(Runnable runnable) {
        Map<String, String> contextMap = MDC.getCopyOfContextMap();
        return () -> {
            try {
                if (contextMap!=null) {
                    MDC.setContextMap(contextMap);
                }
                runnable.run();
            } catch (Exception ex) {
                log.error(ex.getMessage(), ex);
            } finally {
                MDC.clear();
            }
        };
    }
}
