package com.fpt.dxg.notification.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.task.TaskExecutor;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

@Configuration
public class ThreadConfig {

    @Bean(name = "mailAsyncTask")
    TaskExecutor mailAsyncTask() {
        ThreadPoolTaskExecutor taskExecutor = new ThreadPoolTaskExecutor();
        taskExecutor.setCorePoolSize(10);//Số thread cho phép chạy cùng lúc
        taskExecutor.setQueueCapacity(500);//Số lệnh có thể chờ trong hàng đợi. Nếu =0 thì tất cả sẽ chạy cùng lúc không phụ thuộc CorePoolSize
        taskExecutor.setThreadNamePrefix("MailAsyncTask");
        taskExecutor.afterPropertiesSet();
        taskExecutor.setTaskDecorator(new MDCTaskDecorator());
        return taskExecutor;
    }
}
