package com.fpt.dxg.notification.config;

import com.amazonaws.auth.AWSStaticCredentialsProvider;
import com.amazonaws.auth.BasicAWSCredentials;
import com.amazonaws.regions.Regions;
import com.amazonaws.services.simpleemail.AmazonSimpleEmailService;
import com.amazonaws.services.simpleemail.AmazonSimpleEmailServiceClientBuilder;
import com.fpt.dxg.notification.util.ValidationUtil;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import static com.fpt.dxg.notification.constant.Constants.DEFAULT_REGION;


@Configuration
@ConditionalOnProperty(name = "mail.provider", havingValue = "ses")
public class SESConfig {
    @Value("${aws.ses.accessKeyId}")
    private String accessKeyId;
    @Value("${aws.ses.secretKey}")
    private String secretKey;
    @Value("${aws.ses.region}")
    private String region;

    @Bean
    public AmazonSimpleEmailService amazonSimpleEmailService() {
        if(ValidationUtil.isNullOrEmpty(region)){
            region = DEFAULT_REGION;
        }
        BasicAWSCredentials awsCredentials = new BasicAWSCredentials(accessKeyId, secretKey);
        return AmazonSimpleEmailServiceClientBuilder.standard()
                .withRegion(Regions.valueOf(region))
                .withCredentials(new AWSStaticCredentialsProvider(awsCredentials))
                .build();
    }
}
