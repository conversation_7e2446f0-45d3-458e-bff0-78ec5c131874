package com.fpt.dxg.notification.mail;

import lombok.Data;

import java.util.Date;

@Data
public class MeetingRequest {
    private String title;
    private String location;
    private Date start;
    private Date end;
    private String emailOrganize;
    private String recurrenceRule;

    public MeetingRequest(){}

    public MeetingRequest(String title, String location, Date start, Date end, String emailOrganize) {
        this.title = title;
        this.location = location;
        this.start = start;
        this.end = end;
        this.emailOrganize = emailOrganize;
    }

}
