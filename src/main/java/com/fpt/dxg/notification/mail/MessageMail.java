package com.fpt.dxg.notification.mail;

import lombok.Data;

import java.util.Date;

@Data
public class MessageMail extends MessageMailRequest {
    private Long notiId;
    private String domain;
    private String sender;
    private String folderAttachments;
    private Long timeLimit;
    private Long countLimit;
    private String form;
    private String account;
    private Date scheduleAt;
    private Boolean autoSendWhenFail = false;
    private Long cancelSendDay = 0L;
}
