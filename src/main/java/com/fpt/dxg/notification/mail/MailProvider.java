package com.fpt.dxg.notification.mail;

import com.amazonaws.services.simpleemail.AmazonSimpleEmailService;
import com.amazonaws.services.simpleemail.model.RawMessage;
import com.amazonaws.services.simpleemail.model.SendRawEmailRequest;
import com.fpt.dxg.notification.exception.NoSenderException;
import com.fpt.dxg.notification.util.ValidationUtil;
import jakarta.activation.MailcapCommandMap;
import jakarta.activation.MimetypesFileTypeMap;
import jakarta.mail.*;
import jakarta.mail.internet.InternetAddress;
import jakarta.mail.internet.MimeBodyPart;
import jakarta.mail.internet.MimeMessage;
import jakarta.mail.internet.MimeMultipart;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.nio.ByteBuffer;
import java.util.Properties;

import static com.fpt.dxg.notification.mail.SESMail.*;

@Slf4j
@Component
@RequiredArgsConstructor
public class MailProvider {

    private final AmazonSimpleEmailService simpleEmailService;

    @Autowired(required = false)
    private JavaMailSender javaMailSender;

    @Value("${mail.provider}")
    private String mailProvider;

    @Async("mailAsyncTask")
    public void sendMail(MessageMail msg) throws Exception {
        log.info("========== START SEND {} MAIL =========", mailProvider.toUpperCase());
        log.info("Subject: {}", msg.getSubject());
        log.info("Sender: {}", msg.getSender());
        log.info("Receives: {}", String.join(";", msg.getReceives()));

        SESMail mail = new SESMail(msg);

        if (mailProvider.equalsIgnoreCase("smtp")) {
            this.sendSmtpMail(mail);
        } else {
            this.sendRawMail(mail);
        }

        log.info("========== END SEND {} MAIL =========", mailProvider.toUpperCase());
    }

    private void sendSmtpMail(SESMail mail) throws Exception {
        if (ValidationUtil.isNullOrEmpty(mail.getSender())) {
            throw new NoSenderException();
        }

        if (javaMailSender == null) {
            throw new IllegalStateException("JavaMailSender not configured for SMTP");
        }

        MimeMessage message = javaMailSender.createMimeMessage();
        buildMimeMessage(message, mail);

        long start = System.currentTimeMillis();
        try {
            log.info("Start sending SMTP email ...");
            javaMailSender.send(message);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw e;
        } finally {
            log.info("end send SMTP email time {} ms", System.currentTimeMillis() - start);
        }
    }

    private void sendRawMail(SESMail mail) throws Exception {
        if (ValidationUtil.isNullOrEmpty(mail.getSender())) {
            throw new NoSenderException();
        }

        Session session = Session.getDefaultInstance(new Properties());
        MimeMessage message = new MimeMessage(session);
        buildMimeMessage(message, mail);

        ByteArrayOutputStream outputStream = null;
        long start = System.currentTimeMillis();
        try {
            log.info("Start sending SES email ...");
            outputStream = new ByteArrayOutputStream();
            message.writeTo(outputStream);
            RawMessage rawMessage = new RawMessage(ByteBuffer.wrap(outputStream.toByteArray()));
            SendRawEmailRequest rawEmailRequest = new SendRawEmailRequest(rawMessage);
            simpleEmailService.sendRawEmail(rawEmailRequest);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw e;
        } finally {
            log.info("end send SES email time {} ms", System.currentTimeMillis() - start);
            try {
                if (outputStream != null) {
                    outputStream.close();
                }
            } catch (IOException e) {
                log.error("Error closing outputStream");
            }
        }
    }

    private void buildMimeMessage(MimeMessage message, SESMail mail) throws Exception {
        // Add subject and sender
        message.setFrom(new InternetAddress(mail.getSender()));
        message.setSubject(mail.getSubject(), defaultCharset);

        // Add recipients
        message.setRecipients(Message.RecipientType.TO, getRecipient(mail.getRecipient()));
        if (!ValidationUtil.isNullOrEmpty(mail.getCc())) {
            message.setRecipients(Message.RecipientType.CC, getRecipient(mail.getCc()));
        }
        if (!ValidationUtil.isNullOrEmpty(mail.getBcc())) {
            message.setRecipients(Message.RecipientType.BCC, getRecipient(mail.getBcc()));
        }

        // Build email content
        MimeMultipart msg = new MimeMultipart("mixed");

        // HTML content
        MimeBodyPart htmlPart = new MimeBodyPart();
        htmlPart.setContent(mail.getContent(), htmlType);
        msg.addBodyPart(htmlPart);

        // Attachments
        if (ValidationUtil.notEmpty(mail.getAttachedFiles())) {
            for (MimeBodyPart attachedFile : mail.getAttachedFiles()) {
                msg.addBodyPart(attachedFile);
            }
        }

        // Meeting request
        if (!ValidationUtil.isNullOrEmpty(mail.getMeetingRequest())) {
            MimetypesFileTypeMap mimetypes = (MimetypesFileTypeMap) MimetypesFileTypeMap.getDefaultFileTypeMap();
            mimetypes.addMimeTypes("text/calendar ics ICS");
            MailcapCommandMap mailcap = (MailcapCommandMap) MailcapCommandMap.getDefaultCommandMap();
            mailcap.addMailcap("text/calendar;; x-java-content-handler=com.sun.mail.handlers.text_plain");

            message.addHeaderLine("method=REQUEST");
            message.addHeaderLine("charset=UTF-8");
            message.addHeaderLine("component=VEVENT");

            BodyPart calendarPart = buildCalendarPart(mail.getMeetingRequest());
            msg.addBodyPart(calendarPart);
        }

        message.setContent(msg);
    }

    // Test method - remove in production
    public static void main(String[] args) {
        try {
            // SMTP configuration
            String smtpHost = "moj.gov.vn";
            int smtpPort = 465;
            String smtpUsername = "<EMAIL>";
            String smtpPassword = "On9TY@U7";

            Properties props = new Properties();
            props.put("mail.smtp.host", smtpHost);
            props.put("mail.smtp.port", smtpPort);
            props.put("mail.smtp.auth", "true");
            props.put("mail.smtp.starttls.enable", "true");

            Session session = Session.getInstance(props, new Authenticator() {
                @Override
                protected PasswordAuthentication getPasswordAuthentication() {
                    return new PasswordAuthentication(smtpUsername, smtpPassword);
                }
            });

            MimeMessage message = new MimeMessage(session);
            message.setFrom(new InternetAddress("<EMAIL>"));
            message.setSubject("Test gửi mail hệ thống xây dựng pháp luật", "UTF-8");
            message.setRecipients(Message.RecipientType.TO,
                    InternetAddress.parse("<EMAIL>"));

            MimeMultipart multipart = new MimeMultipart("mixed");
            MimeBodyPart htmlPart = new MimeBodyPart();
            htmlPart.setContent("<p>Test gửi mail hệ thống xây dựng pháp luật</p>",
                    "text/html; charset=UTF-8");
            multipart.addBodyPart(htmlPart);

            message.setContent(multipart);

            System.out.println("Sending test email...");
            Transport.send(message);
            System.out.println("Email sent successfully!");

        } catch (Exception e) {
            System.err.println("Error: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
