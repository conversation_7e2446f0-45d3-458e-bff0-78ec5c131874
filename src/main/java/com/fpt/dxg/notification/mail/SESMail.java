package com.fpt.dxg.notification.mail;

import com.fpt.dxg.notification.util.ValidationUtil;
import jakarta.activation.DataHandler;
import jakarta.activation.DataSource;
import jakarta.activation.FileDataSource;
import jakarta.mail.BodyPart;
import jakarta.mail.internet.AddressException;
import jakarta.mail.internet.InternetAddress;
import jakarta.mail.internet.MimeBodyPart;
import jakarta.mail.util.ByteArrayDataSource;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FilenameUtils;

import java.io.ByteArrayInputStream;
import java.net.URL;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Base64;
import java.util.List;
import java.util.UUID;

@Slf4j
@Getter
@Setter
public class SESMail {
    public static String htmlType = "text/html; charset=UTF-8";
    public static String textType = "text/plain; charset=UTF-8";
    public static String defaultCharset = "UTF-8";
    private static String CONFIGURATION_SET = "ConfigSet";

    // "From" address. This address must be verified with Amazon SES.
    private String sender;

    private String[] recipient;
    private String[] cc;
    private String[] bcc;

    private String subject;
    private String content;

    private List<MimeBodyPart> attachedFiles;
    private MeetingRequest meetingRequest;

    public SESMail(String sender) {
        this.sender = sender;
    }

    public SESMail(String sender, String subject, String content, String[] recipient) {
        this.sender = sender;
        this.subject = subject;
        this.content = content;
        this.recipient = recipient;
    }

    public SESMail(MessageMail mMail) throws Exception {
        this.sender = mMail.getSender();
        this.subject = mMail.getSubject();
        this.content = mMail.getContent();
        this.recipient = mMail.getReceives();
        this.cc = mMail.getCcReceives();
        this.bcc = mMail.getBccReceives();
        this.meetingRequest = mMail.getMeetingRequest();
        this.attachedFiles = new ArrayList<>();
        if (ValidationUtil.notEmpty(mMail.getAttachments())) {
            log.info("Processed Attachment");
            for (Attachment attachment : mMail.getAttachments()) {
                this.addAttachedFile(attachment.getFileName(), attachment.getBase64());
            }
        }
    }

    public void addAttachedFile(String strFileName) throws Exception {
        MimeBodyPart mbp = new MimeBodyPart();
        FileDataSource fds = new FileDataSource(strFileName);
        mbp.setDataHandler(new DataHandler(fds));
        mbp.setDisposition("attachment");
        mbp.setFileName(fds.getName());
        this.attachedFiles.add(mbp);
    }

    public void addAttachedFile(String strFileName, String strDataFile) throws Exception {
        MimeBodyPart mbp = new MimeBodyPart();
        DataSource source = new ByteArrayDataSource(new ByteArrayInputStream(Base64.getDecoder().decode(strDataFile)), "application/octet-stream");
        mbp.setDataHandler(new DataHandler(source));
        mbp.setDisposition("attachment");
        mbp.setFileName(strFileName);
        this.attachedFiles.add(mbp);
    }


    public void addAttachedFileFromUrl(String url) throws Exception {
        URL fileUrl = new URL(url);
        MimeBodyPart mbp = new MimeBodyPart();
        mbp.setDataHandler(new DataHandler(fileUrl));
        mbp.setDisposition("attachment");
        mbp.setFileName(FilenameUtils.getName(fileUrl.getPath()));
        this.attachedFiles.add(mbp);
    }

    public static BodyPart buildCalendarPart(MeetingRequest obj) throws Exception {
        SimpleDateFormat iCalendarDateFormat = new SimpleDateFormat("yyyyMMdd'T'HHmm'00'");
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMddHHmmssSSS");
        BodyPart calendarPart = new MimeBodyPart();

        // Tạo UID an toàn và đúng chuẩn
        String uid = UUID.randomUUID().toString() + "@ftu.vn";

        // Tạo dòng RRULE nếu có
        String rruleLine = "";
        if (!ValidationUtil.isNullOrEmpty(obj.getRecurrenceRule())) {
            rruleLine = "RRULE:" + obj.getRecurrenceRule();
        }

        String calendarContent = CALENDAR_MEETING_REQUEST_TEMPLATE
                .replaceAll("@P_DTSTART", iCalendarDateFormat.format(obj.getStart()))
                .replaceAll("@P_DTEND", iCalendarDateFormat.format(obj.getEnd()))
                .replaceAll("@P_SUMMARY", obj.getTitle())
                .replaceAll("@P_ORGANIZER", obj.getEmailOrganize())
                .replaceAll("@P_UID", uid)
                .replaceAll("@P_LOCATION", obj.getLocation())
                .replaceAll("@P_RRULE_LINE", rruleLine);

        calendarPart.addHeader("Content-Class", "urn:content-classes:calendarmessage");
        calendarPart.setContent(calendarContent, "text/calendar;method=REQUEST;charset=utf-8");
        return calendarPart;
    }


    public static InternetAddress[] getRecipient(String[] lstRecipient) throws Exception {
        List<InternetAddress> result = new ArrayList<>();
        if (ValidationUtil.isNullOrEmpty(lstRecipient)) {
            return null;
        } else {
            int i = 0;
            try {
                for (i = 0; i < lstRecipient.length; i++) {
                    result.add(new InternetAddress(lstRecipient[i]));
                }
            } catch (AddressException e) {
                throw new AddressException("Recipient " + lstRecipient[i] + " is not valid");
            }
            return result.toArray(new InternetAddress[0]);
        }
    }

    public static final String CALENDAR_MEETING_REQUEST_TEMPLATE
            = "BEGIN:VCALENDAR\n"
            + "METHOD:REQUEST\n"
            + "PRODID:Meeting Room - FTU\n"
            + "VERSION:2.0\n"
            + "BEGIN:VTIMEZONE\n"
            + "TZID:SE Asia Standard Time\n"
            + "BEGIN:STANDARD\n"
            + "DTSTART:@P_DTSTART\n"
            + "TZOFFSETFROM:+0700\n"
            + "TZOFFSETTO:+0700\n"
            + "END:STANDARD\n"
            + "BEGIN:DAYLIGHT\n"
            + "DTSTART:@P_DTSTART\n"
            + "TZOFFSETFROM:+0700\n"
            + "TZOFFSETTO:+0700\n"
            + "END:DAYLIGHT\n"
            + "END:VTIMEZONE\n"
            + "BEGIN:VEVENT\n"
            + "DTSTAMP:@P_DTSTART\n"
            + "DTSTART:@P_DTSTART\n"
            + "DTEND:@P_DTEND\n"
            + "@P_RRULE_LINE\n"
            + "SUMMARY:@P_SUMMARY\n"
            + "UID:@P_UID\n"
            + "ORGANIZER:MAILTO:@P_ORGANIZER\n"
            + "LOCATION:@P_LOCATION\n"
            + "SEQUENCE:0\n"
            + "PRIORITY:5\n"
            + "CLASS:PUBLIC\n"
            + "STATUS:CONFIRMED\n"
            + "TRANSP:OPAQUE\n"
            + "BEGIN:VALARM\n"
            + "TRIGGER:-PT15M\n"
            + "ACTION:DISPLAY\n"
            + "DESCRIPTION:Reminder for @P_SUMMARY\n"
            + "END:VALARM\n"
            + "END:VEVENT\n"
            + "END:VCALENDAR";

}
