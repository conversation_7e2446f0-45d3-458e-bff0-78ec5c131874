package com.fpt.dxg.notification.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fpt.dxg.notification.constant.Constants;
import com.fpt.dxg.notification.util.DateUtil;
import lombok.Data;
import org.apache.kafka.common.security.oauthbearer.internals.secured.ValidateException;

import java.util.Date;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class MessagesResponse<T> {

    public interface RESPONSE_STATUS {
        String SUCCESS = "SUCCESS";
        String ERROR = "ERROR";
    }

    private String code;
    private String message;
    private String status;
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String timestamp;
    private T data;

    public MessagesResponse(){
        this.timestamp = DateUtil.dateToString(new Date(), DateUtil.FORMAT_24H);
        this.code = Constants.SUCCESS;
        this.status = RESPONSE_STATUS.SUCCESS;
    }

    public MessagesResponse<T> error(Exception e){
        this.timestamp = DateUtil.dateToString(new Date(), DateUtil.FORMAT_24H);
        this.status = RESPONSE_STATUS.ERROR;
        if (e instanceof ValidateException){
            this.code = Constants.SYSTEM_ERROR;
            this.message = e.toString();
        } else {
            this.code = Constants.SYSTEM_ERROR;
            this.message = e.getMessage();
        }
        return this;
    }

}
