package com.fpt.dxg.notification.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fpt.dxg.notification.constant.NotificationType;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;

@AllArgsConstructor
@NoArgsConstructor
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class NotificationMessage {
    @JsonIgnore
    String id;
    long receiveTime;
    String receiver;
    String type;
    Object payload;
    Object extraInfo;
    String title;
    String system;
}
