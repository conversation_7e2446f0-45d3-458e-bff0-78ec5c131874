package com.fpt.dxg.notification.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fpt.dxg.notification.mail.MessageMail;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;
import lombok.experimental.FieldDefaults;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.Date;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@FieldDefaults(level = lombok.AccessLevel.PRIVATE)
@Accessors(chain = true)
@Document(collection = "mail_history")
public class MailHistory {

    @Id
    String id;
    Date sendDate;
    boolean status;
    String receiver;
    String server;
    String emailSender;
    Long notiId;
    String reason;
    String title;
    String content;
    boolean isRetry;

    public MailHistory(MessageMail mail, String messageLog, boolean status, boolean isRetry) {
        this.isRetry = isRetry;
        this.notiId = mail.getNotiId();
        this.reason = messageLog;
        this.status = status;
        this.server = mail.getDomain();
        this.sendDate = new Date();
        this.content = mail.getContent();
        this.title = mail.getSubject();
        this.receiver = String.join(", ", mail.getReceives());
        this.emailSender = mail.getSender();
    }
}
