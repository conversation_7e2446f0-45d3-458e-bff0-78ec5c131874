package com.fpt.dxg.notification.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fpt.dxg.notification.constant.NotificationType;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;
import lombok.experimental.FieldDefaults;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.io.Serializable;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(Include.NON_NULL)
@FieldDefaults(level = lombok.AccessLevel.PRIVATE)
@Accessors(chain = true)
@Document(collection = "notifications")
public class Notification implements Serializable {
    @Id
    String id;
    long receiveTime;
    String receiver;
    String type;
    Object payload;
    Object extraInfo;
    boolean seen;
}
