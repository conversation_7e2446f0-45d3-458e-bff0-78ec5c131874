package com.fpt.dxg.notification.handler.message;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fpt.dxg.notification.constant.NotificationTopic;
import com.fpt.dxg.notification.handler.AbstractNotificationHandler;
import com.fpt.dxg.notification.mail.MessageMail;
import com.fpt.dxg.notification.model.NotificationMessage;
import com.fpt.dxg.notification.service.MailRetryService;
import com.fpt.dxg.notification.service.MailService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.kafka.support.KafkaHeaders;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.stereotype.Service;

/**
 * Kafka consumer for noti topic
 */
@Service
@EnableCaching
@ConditionalOnProperty(name = "kafka.noti.event.listener.enabled", havingValue = "true")
public class NotificationHandler extends AbstractNotificationHandler {

    private static final Logger LOGGER = LoggerFactory.getLogger(NotificationHandler.class);
    private final ObjectMapper mapper;
    private final MailService mailService;
    private final MailRetryService mailRetryService;

    @Autowired
    public NotificationHandler(ObjectMapper mapper,
                               MailService mailService,
                               MailRetryService mailRetryService) {
        this.mapper = mapper;
        this.mailService = mailService;
        this.mailRetryService = mailRetryService;
    }

    @KafkaListener(topics = NotificationTopic.EMAIL)
    public void onNotificationEmail(String payload, @Header(KafkaHeaders.RECEIVED_TIMESTAMP) Long ts, Acknowledgment ack) {
        MessageMail messageMail = null;
        String messageLog = null;
        boolean status = false;
        boolean isRetry = false;
        try {
            NotificationMessage message = mapper.readValue(payload, NotificationMessage.class);
            LOGGER.info("--- Receive message from topic email: {} {} ", message, ts);

            messageMail = mapper.convertValue(message.getPayload(), MessageMail.class);
            mailService.sendMail(messageMail);

            messageLog = "Success";
            status = true;
        } catch (Exception e) {
            messageLog = "Send mail fail. Detail: " + e.getMessage();
            mailRetryService.saveMailRetry(messageMail, payload);
        } finally {
            mailService.saveMailHistory(messageMail, messageLog, status, isRetry);
            ack.acknowledge();
        }
    }

}

