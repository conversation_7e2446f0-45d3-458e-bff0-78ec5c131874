package com.fpt.dxg.notification.handler;

import com.fpt.dxg.notification.exception.InvalidPayloadException;
import com.fpt.dxg.notification.model.NotificationMessage;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.kafka.support.Acknowledgment;

public abstract class AbstractNotificationHandler implements EventReceiver {

    private static final Logger LOGGER = LoggerFactory.getLogger(AbstractNotificationHandler.class);

    @Override
    public void onEvent(String payload, Long ts, Acknowledgment ack) {
        try {
            ack.acknowledge();
        } catch (InvalidPayloadException ipe) {
            LOGGER.debug("invalid payload {}", ExceptionUtils.getStackTrace(ipe));
            ack.acknowledge();
        } catch (Exception e) {
            LOGGER.debug("error processing event: {}, with error: {}", payload, ExceptionUtils.getStackTrace(e));
            throw e;
        } finally {
            MDC.clear();
        }
    }


    /**
     * Default is do nothing. It is to add custom code for any special handling of the event by the receiver...
     *
     * @param notificationMessage
     */
    protected void postProcess(NotificationMessage notificationMessage) {
    }


    protected boolean isProcessable(String payload) {
        return true;
    }

}
