package com.fpt.dxg.notification.service.impl;

import com.fpt.dxg.notification.service.CountingService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.stereotype.Service;

@Service
public class CountingServiceImpl implements CountingService {

    private final ValueOperations<String, String> valueOps;

    @Autowired
    public CountingServiceImpl(StringRedisTemplate stringRedisTemplate) {
        this.valueOps = stringRedisTemplate.opsForValue();
    }

    private int getCount(String username) {
        String data = valueOps.get(username);
        int count = 0;
        if (data != null) {
            count = Integer.parseInt(data);
        }
        return count;
    }

    @Override
    public void incrementCount(String username) {
        valueOps.increment(username, 1);
    }

}