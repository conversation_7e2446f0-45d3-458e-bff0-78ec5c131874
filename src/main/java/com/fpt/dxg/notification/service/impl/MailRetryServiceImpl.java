package com.fpt.dxg.notification.service.impl;

import com.fpt.dxg.notification.constant.Constants;
import com.fpt.dxg.notification.mail.MessageMail;
import com.fpt.dxg.notification.model.MailRetry;
import com.fpt.dxg.notification.repository.MailRetryRepository;
import com.fpt.dxg.notification.service.MailRetryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

@Service
public class MailRetryServiceImpl implements MailRetryService {

    private final MailRetryRepository mailRetryRepository;

    @Autowired
    public MailRetryServiceImpl(MailRetryRepository mailRetryRepository) {
        this.mailRetryRepository = mailRetryRepository;
    }


    @Override
    public List<MailRetry> getAllMailNeedRetry() {
        long current = new Date().getTime();
        return mailRetryRepository.findAllByExpiredTimeAfterAndStatus(current, Constants.STATUS_PENDING);
    }

    @Override
    public void saveListMailRetry(List<MailRetry> mailRetries) {
        mailRetryRepository.saveAll(mailRetries);
    }

    @Override
    public void saveMailRetry(MailRetry mailRetry) {
        mailRetryRepository.save(mailRetry);
    }

    @Override
    public void saveMailRetry(MessageMail messageMail, String payload) {
        MailRetry mailRetry = new MailRetry();
        if(messageMail != null && messageMail.getAutoSendWhenFail()) {
            long expiredTime = new Date().getTime() + messageMail.getCancelSendDay()*24*60*60*1000;

            mailRetry.setStatus(Constants.STATUS_PENDING);
            mailRetry.setExpiredTime(expiredTime);
            mailRetry.setPayload(payload);

            mailRetryRepository.save(mailRetry);
        }
    }


}
