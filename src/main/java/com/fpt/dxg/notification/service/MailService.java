package com.fpt.dxg.notification.service;

import com.fpt.dxg.notification.mail.MessageMail;
import com.fpt.dxg.notification.model.MailHistory;

import java.util.List;

public interface MailService {

    void saveMailHistory(MessageMail mail,String messageLog, boolean status, boolean isRetry);

    boolean checkBandwidthChannel(String type, String channel, Long countLimit, Long timeLimit);

    void sendMail(MessageMail messageMail) throws Exception;

    void saveListMailHistory(List<MailHistory> mailHistoryList);
}
