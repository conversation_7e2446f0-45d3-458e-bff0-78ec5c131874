package com.fpt.dxg.notification.service;

import com.fpt.dxg.notification.mail.MessageMail;
import com.fpt.dxg.notification.model.MailRetry;

import java.util.List;

public interface MailRetryService {

    List<MailRetry> getAllMailNeedRetry();

    void saveListMailRetry(List<MailRetry> mailRetries);

    void saveMailRetry(MailRetry mailRetry);

    void saveMailRetry(MessageMail messageMail, String payload);
}
