package com.fpt.dxg.notification.service.impl;

import com.fpt.dxg.notification.mail.MailProvider;
import com.fpt.dxg.notification.mail.MessageMail;
import com.fpt.dxg.notification.model.BandwidthChannel;
import com.fpt.dxg.notification.model.MailHistory;
import com.fpt.dxg.notification.repository.BandwidthChannelRepository;
import com.fpt.dxg.notification.repository.MailHistoryRepository;
import com.fpt.dxg.notification.service.MailService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

@Service
@Slf4j
public class MailServiceImpl implements MailService {
    private final MailHistoryRepository mailHistoryRepository;
    private final BandwidthChannelRepository bandwidthChannelRepository;
    private final MailProvider mailProvider;

    @Autowired
    public MailServiceImpl(MailHistoryRepository mailHistoryRepository,
                           BandwidthChannelRepository bandwidthChannelRepository,
                           MailProvider mailProvider) {
        this.mailHistoryRepository = mailHistoryRepository;
        this.bandwidthChannelRepository = bandwidthChannelRepository;
        this.mailProvider = mailProvider;
    }

    @Override
    public void saveMailHistory(MessageMail mail,String messageLog, boolean status, boolean isRetry) {
        MailHistory history = new MailHistory();
        history.setEmailSender(mail.getSender());
        history.setReceiver(String.join(", ", mail.getReceives()));
        history.setTitle(mail.getSubject());
        history.setContent(mail.getContent());
        history.setSendDate(new Date());
        history.setServer(mail.getDomain());
        history.setStatus(status);
        history.setReason(messageLog);
        history.setNotiId(mail.getNotiId());
        history.setRetry(isRetry);

        history = mailHistoryRepository.save(history);
        log.info("log mail history {}", history.getId());
    }

    @Override
    public boolean checkBandwidthChannel(String type, String channel, Long countLimit, Long timeLimit) {
        String key = channel + " | " + type;
        Long currentTime = System.currentTimeMillis();
        boolean isSend = false;
        if(countLimit == null || timeLimit == null) {
            return true;
        }

        BandwidthChannel bandwidth = bandwidthChannelRepository.getByKey(key);
        if(bandwidth != null) {
            long totalSent = bandwidth.getTotalPacketSent();
            if(totalSent < countLimit && currentTime <= bandwidth.getExpiredIn()) {
                isSend = true;
                bandwidth.setTotalPacketSent(++totalSent);
            } else if(currentTime > bandwidth.getExpiredIn()) {
                bandwidth.setKey(key);
                bandwidth.setTotalPacketSent(1L);
                bandwidth.setExpiredIn(currentTime + timeLimit);
                isSend = true;
            }
        } else {
            bandwidth = new BandwidthChannel();
            bandwidth.setKey(key);
            bandwidth.setTotalPacketSent(1L);
            bandwidth.setExpiredIn(currentTime + timeLimit);

            isSend = true;
        }
        bandwidthChannelRepository.save(bandwidth);

        return isSend;
    }

    @Override
    public void sendMail(MessageMail messageMail) throws Exception {
        mailProvider.sendMail(messageMail);
    }

    @Override
    public void saveListMailHistory(List<MailHistory> mailHistoryList) {
        mailHistoryRepository.saveAll(mailHistoryList);
    }
}
