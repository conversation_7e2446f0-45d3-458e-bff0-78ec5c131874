package com.fpt.dxg.notification.service.impl;

import com.fpt.dxg.notification.model.Notification;
import com.fpt.dxg.notification.model.NotificationMessage;
import com.fpt.dxg.notification.repository.NotificationRepository;
import com.fpt.dxg.notification.service.CountingService;
import com.fpt.dxg.notification.service.MongoService;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.stereotype.Service;

import java.util.Map;

@Service
@RequiredArgsConstructor
public class MongoServiceImpl implements MongoService {
    private static final Logger log = LogManager.getLogger(MongoServiceImpl.class);

    private final NotificationRepository repository;

    private final CountingService countingService;

    @Override
    public void produce(NotificationMessage notificationMessage) {
        try {
            if (isNeedPersistence(notificationMessage)) {
                Notification notification = createNotification(notificationMessage);
                log.info("Store ----> " + notificationMessage.getReceiver());
                repository.insert(notification);
                log.info("Indexed to Mongodb => " + notification.getId());
                countingService.incrementCount(notificationMessage.getReceiver());
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @SuppressWarnings("unchecked")
    private boolean isNeedPersistence(NotificationMessage msg) {
        try {
            if (msg.getExtraInfo() != null) {
                Map<String, Object> extractInfo = (Map<String, Object>) msg.getExtraInfo();
                Object persistenceFlag = extractInfo.get("isPersistence");
                if (persistenceFlag instanceof Boolean && !(Boolean) persistenceFlag) {
                    return false;
                }
            }
        } catch (Exception e) {
            log.error("extra info is not json object: {}", msg);
        }
        return true;
    }


    private Notification createNotification(NotificationMessage notificationMessage) {
        Notification notification = new Notification();
        notification.setReceiver(notificationMessage.getReceiver());
        notification.setReceiveTime(notificationMessage.getReceiveTime());
        notification.setPayload(notificationMessage.getPayload());
        notification.setType(notificationMessage.getType());
        notification.setExtraInfo(notificationMessage.getExtraInfo());
        notification.setSeen(false);

        return notification;
    }
}
