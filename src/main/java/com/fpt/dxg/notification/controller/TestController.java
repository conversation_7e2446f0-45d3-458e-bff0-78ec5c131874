package com.fpt.dxg.notification.controller;


import com.fpt.dxg.notification.mail.MailProvider;
import com.fpt.dxg.notification.mail.MessageMail;
import com.fpt.dxg.notification.service.MailRetryService;
import com.fpt.dxg.notification.service.MailService;
import com.fpt.dxg.notification.task.NotificationRetryTask;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping("/test")
@RequiredArgsConstructor
public class TestController {
    private final MailProvider mailProvider;
    private final MailService mailService;
    private final MailRetryService mailRetryService;
    private final NotificationRetryTask notificationRetryTask;

    @PostMapping("/sendEmail")
    public ResponseEntity<?> testSendEmail(@RequestBody MessageMail email) throws Exception {
        mailProvider.sendMail(email);
        mailService.saveMailHistory(email, "khoadt", true, false);
        return ResponseEntity.ok("ok");
    }
}
