mvn clean package -DskipTests=true
call docker build -t eapprove/noti-email:latest .
call docker tag eapprove/noti-email:latest *************:5000/eapprove/noti-email:latest
call docker push *************:5000/eapprove/noti-email:latest

//btp
call docker build -t ************************************/eapprove/noti-email:latest .
call docker tag ************************************/eapprove/noti-email:latest registry-hn02.fke.fptcloud.com/************************************/eapprove/noti-email:latest
call docker push registry-hn02.fke.fptcloud.com/************************************/eapprove/noti-email:latest